# SharePoint ファイルインベントリスクリプト
# SharePoint Online/On-premises サイトからファイルメタデータを抽出し、CSV形式で出力する

param(
    [Parameter(Mandatory = $false, HelpMessage = "SharePoint ライブラリ設定ファイルのパス")]
    [string]$ConfigFile = "./sharepoint_config.txt",

    [Parameter(Mandatory = $false, HelpMessage = "認証情報JSONファイルのパス")]
    [string]$CredentialsFile = "./credentials.json",

    [Parameter(Mandatory = $false, HelpMessage = "CSV出力ディレクトリ")]
    [string]$OutputDirectory = "./output",

    [Parameter(Mandatory = $false, HelpMessage = "CSVバッチサイズ(デフォルト: 5000)")]
    [int]$BatchSize = 5000,

    [Parameter(Mandatory = $false, HelpMessage = "詳細ログを有効にする")]
    [switch]$VerboseLogging = $false,

    [Parameter(Mandatory = $false, HelpMessage = "処理済みリポジトリを自動スキップ")]
    [switch]$ResumeMode = $false,

    [Parameter(Mandatory = $false, HelpMessage = "フォルダスキャンのページサイズ(デフォルト: 200)")]
    [int]$PageSize = 200,

    [Parameter(Mandatory = $false, HelpMessage = "エラー時の最大リトライ回数(デフォルト: 3)")]
    [int]$MaxRetries = 3,

    [Parameter(Mandatory = $false, HelpMessage = "リトライ間隔（秒）(デフォルト: 5)")]
    [int]$RetryDelaySeconds = 5,

    [Parameter(Mandatory = $false, HelpMessage = "最大フォルダ深度(デフォルト: 20)")]
    [int]$MaxFolderDepth = 20
)

# グローバル変数の初期化
$script:ProcessedFiles = 0
$script:TotalEstimatedFiles = 0
$script:StartTime = Get-Date
$script:CurrentLibrary = ""

# ログ関数
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage }
    }

    if ($VerboseLogging) {
        $logFile = Join-Path $OutputDirectory "SharePoint-FileList.log"
        Add-Content -Path $logFile -Value $logMessage -Encoding UTF8
    }
}

# リトライ機能付き実行関数
function Invoke-WithRetry {
    param(
        [scriptblock]$ScriptBlock,
        [int]$MaxRetries = $script:MaxRetries,
        [int]$DelaySeconds = $script:RetryDelaySeconds,
        [string]$OperationName = "Operation"
    )

    $attempt = 1
    $lastError = $null

    while ($attempt -le $MaxRetries) {
        try {
            Write-Log "Attempting $OperationName (attempt $attempt/$MaxRetries)"
            $result = & $ScriptBlock
            Write-Log "$OperationName succeeded on attempt $attempt" "SUCCESS"
            return $result
        }
        catch {
            $lastError = $_
            Write-Log "$OperationName failed on attempt $attempt`: $($_.Exception.Message)" "WARNING"

            if ($attempt -lt $MaxRetries) {
                Write-Log "Waiting $DelaySeconds seconds before retry..." "INFO"
                Start-Sleep -Seconds $DelaySeconds
            }

            $attempt++
        }
    }

    Write-Log "$OperationName failed after $MaxRetries attempts" "ERROR"
    throw $lastError
}

# メモリ使用量監視関数
function Get-MemoryUsage {
    $process = Get-Process -Id $PID
    $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
    return $memoryMB
}

# ガベージコレクション実行関数
function Invoke-GarbageCollection {
    param([string]$Context = "")

    $beforeMB = Get-MemoryUsage
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
    [System.GC]::Collect()
    $afterMB = Get-MemoryUsage

    $freedMB = $beforeMB - $afterMB
    Write-Log "Memory cleanup $Context`: Before: ${beforeMB}MB, After: ${afterMB}MB, Freed: ${freedMB}MB" "INFO"
}

# 進捗表示関数
function Show-Progress {
    param(
        [int]$Current,
        [int]$Total,
        [string]$Activity
    )
    
    if ($Total -gt 0) {
        $percent = [math]::Round(($Current / $Total) * 100, 2)
        $elapsed = (Get-Date) - $script:StartTime
        
        if ($Current -gt 0) {
            $estimatedTotal = $elapsed.TotalSeconds * ($Total / $Current)
            $remaining = [TimeSpan]::FromSeconds($estimatedTotal - $elapsed.TotalSeconds)
            $eta = "ETA: {0:hh\:mm\:ss}" -f $remaining
        }
        else {
            $eta = "ETA: Calculating..."
        }
        
        Write-Progress -Activity $Activity -Status "$Current/$Total files processed ($percent%) - $eta" -PercentComplete $percent
    }
}

# 証明書認証情報読み込み関数
function Get-CertificateCredentials {
    param([string]$CredentialsPath)
    
    try {
        if (-not (Test-Path $CredentialsPath)) {
            throw "Credentials file not found: $CredentialsPath"
        }
        
        $credJson = Get-Content $CredentialsPath -Raw -Encoding UTF8 | ConvertFrom-Json
        
        # 証明書認証に必要な情報をチェック
        if (-not $credJson.clientId -or -not $credJson.certificatePath -or -not $credJson.tenantId) {
            throw "Invalid certificate credentials format. Required: clientId, certificatePath, tenantId"
        }
        
        # 証明書ファイルの存在確認
        if (-not (Test-Path $credJson.certificatePath)) {
            throw "Certificate file not found: $($credJson.certificatePath)"
        }
        
        $certCredentials = @{
            ClientId            = $credJson.clientId
            CertificatePath     = $credJson.certificatePath
            CertificatePassword = $credJson.certificatePassword
            TenantId            = $credJson.tenantId
        }
        
        Write-Log "Certificate credentials loaded successfully for ClientId: $($credJson.clientId)" "SUCCESS"
        Write-Log "Certificate path: $($credJson.certificatePath)" "SUCCESS"
        return $certCredentials
        
    }
    catch {
        Write-Log "Failed to load certificate credentials: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 設定ファイル読み込み関数
function Get-SharePointConfig {
    param([string]$ConfigPath)
    
    try {
        if (-not (Test-Path $ConfigPath)) {
            throw "Configuration file not found: $ConfigPath"
        }
        
        $configs = @()
        $lines = Get-Content $ConfigPath -Encoding UTF8
        
        foreach ($line in $lines) {
            $line = $line.Trim()
            if ($line -and -not $line.StartsWith("#")) {
                $parts = $line -split '\|'
                if ($parts.Count -eq 3) {
                    $configs += [PSCustomObject]@{
                        SiteUrl     = $parts[0].Trim()
                        LibraryName = $parts[1].Trim()
                        TargetPath  = $parts[2].Trim()
                    }
                }
                else {
                    Write-Log "Invalid configuration line format: $line" "WARNING"
                }
            }
        }
        
        Write-Log "Loaded $($configs.Count) SharePoint library configurations" "SUCCESS"
        return $configs
        
    }
    catch {
        Write-Log "Failed to load configuration: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 出力ファイル名生成関数
function Get-OutputFileName {
    param(
        [string]$SiteUrl,
        [string]$LibraryName,
        [string]$TargetPath,
        [int]$BatchNumber = 0
    )

    $siteName = ($SiteUrl -split '/')[-1] -replace '[^\w\-_]', '_'
    $libraryName = $LibraryName -replace '[^\w\-_]', '_'
    
    # TargetPathの処理を改善
    $targetDir = if ($TargetPath -eq "/" -or $TargetPath -eq "" -or $TargetPath -eq $null) {
        ""
    }
    else {
        ($TargetPath.TrimStart('/').TrimEnd('/') -replace '[^\w\-_/]', '_') -replace '/', '_'
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

    if ($BatchNumber -gt 0) {
        return "${siteName}_${libraryName}_${targetDir}_batch_${BatchNumber}_${timestamp}.csv"
    }
    else {
        return "${siteName}_${libraryName}_${targetDir}_${timestamp}.csv"
    }
}

# 既存出力ファイルチェック関数
function Test-ExistingOutput {
    param(
        [string]$SiteUrl,
        [string]$LibraryName,
        [string]$TargetPath
    )
    
    $siteName = ($SiteUrl -split '/')[-1] -replace '[^\w\-_]', '_'
    $libraryName = $LibraryName -replace '[^\w\-_]', '_'
    
    # TargetPathの処理を統一
    $targetDir = if ($TargetPath -eq "/" -or $TargetPath -eq "" -or $TargetPath -eq $null) {
        ""
    }
    else {
        ($TargetPath.TrimStart('/').TrimEnd('/') -replace '[^\w\-_/]', '_') -replace '/', '_'
    }
    
    $pattern = "${siteName}_${libraryName}_${targetDir}_*.csv"
    $existingFiles = Get-ChildItem -Path $OutputDirectory -Filter $pattern -ErrorAction SilentlyContinue
    
    return $existingFiles.Count -gt 0
}

# CSV書き込み関数
function Write-CsvBatch {
    param(
        [array]$FileData,
        [string]$SiteUrl,
        [string]$LibraryName,
        [string]$TargetPath,
        [string]$OutputDir,
        [bool]$IsFirstBatch,
        [int]$BatchNumber = 0
    )

    try {
        if ($FileData.Count -eq 0) { return }

        # ファイル名を生成
        $fileName = Get-OutputFileName -SiteUrl $SiteUrl -LibraryName $LibraryName -TargetPath $TargetPath -BatchNumber $BatchNumber
        $finalOutputPath = Join-Path $OutputDir $fileName

        if ($IsFirstBatch) {
            # ヘッダー付きで新規作成
            $FileData | Export-Csv -Path $finalOutputPath -NoTypeInformation -Encoding UTF8
        }
        else {
            # ヘッダーなしで追記
            $FileData | ConvertTo-Csv -NoTypeInformation | Select-Object -Skip 1 |
            Add-Content -Path $finalOutputPath -Encoding UTF8
        }

        Write-Log "Wrote $($FileData.Count) records to CSV: $fileName" "SUCCESS"

        return $finalOutputPath

    }
    catch {
        Write-Log "Failed to write CSV batch: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# SharePoint 証明書認証接続関数
function Connect-SharePointSite {
    param(
        [string]$SiteUrl,
        [hashtable]$CertCredentials
    )

    try {
        Write-Log "Connecting to SharePoint site using certificate authentication: $SiteUrl"
        Write-Log "Using ClientId: $($CertCredentials.ClientId)"
        Write-Log "Using Certificate: $($CertCredentials.CertificatePath)"

        # 証明書パスワードの処理
        $securePassword = $null
        if ($CertCredentials.CertificatePassword) {
            if ($CertCredentials.CertificatePassword -is [SecureString]) {
                $securePassword = $CertCredentials.CertificatePassword
            }
            else {
                $securePassword = ConvertTo-SecureString -String $CertCredentials.CertificatePassword -AsPlainText -Force
            }
        }

        # 証明書認証でSharePoint接続
        if ($securePassword) {
            Connect-PnPOnline -Url $SiteUrl -ClientId $CertCredentials.ClientId -CertificatePath $CertCredentials.CertificatePath -CertificatePassword $securePassword -Tenant $CertCredentials.TenantId -WarningAction SilentlyContinue -ErrorAction Stop
        }
        else {
            Connect-PnPOnline -Url $SiteUrl -ClientId $CertCredentials.ClientId -CertificatePath $CertCredentials.CertificatePath -Tenant $CertCredentials.TenantId -WarningAction SilentlyContinue -ErrorAction Stop
        }
        
        Write-Log "Successfully connected to SharePoint site using certificate authentication" "SUCCESS"
        return $true

    }
    catch {
        Write-Log "Failed to connect to SharePoint using certificate authentication: $($_.Exception.Message)" "ERROR"
        Write-Log "Please verify your App registration, certificate, and permissions in Azure AD" "ERROR"
        return $false
    }
}

# フォルダ再帰処理関数（改善版）
function Get-FoldersRecursively {
    param(
        [string]$LibraryName,
        [string]$FolderPath = "",
        [int]$MaxDepth = $script:MaxFolderDepth,
        [int]$CurrentDepth = 0
    )

    $folders = @()

    try {
        if ($CurrentDepth -ge $MaxDepth) {
            Write-Log "Maximum recursion depth reached: $MaxDepth at path: $FolderPath" "WARNING"
            return $folders
        }

        # 現在のフォルダを追加
        $folders += $FolderPath

        # サブフォルダを取得（リトライ機能付き）
        $subFolders = Invoke-WithRetry -OperationName "Get folders from $FolderPath" -ScriptBlock {
            if ($FolderPath -eq "" -or $FolderPath -eq "/") {
                Get-PnPFolderItem -FolderSiteRelativeUrl $LibraryName -ItemType Folder -ErrorAction Stop
            }
            else {
                $fullPath = "$LibraryName$FolderPath"
                Get-PnPFolderItem -FolderSiteRelativeUrl $fullPath -ItemType Folder -ErrorAction Stop
            }
        }

        $validSubFolders = @()
        foreach ($subFolder in $subFolders) {
            # システムフォルダとSharePointの内部フォルダをスキップ
            if ($subFolder.Name -notmatch "^(Forms|_vti_|\.)" -and
                $subFolder.Name -notin @("Attachments", "Workflows", "Lists", "SiteAssets", "SitePages", "Style Library")) {
                $validSubFolders += $subFolder
            }
        }

        Write-Log "Found $($validSubFolders.Count) valid subfolders in $FolderPath (depth: $CurrentDepth)"

        foreach ($subFolder in $validSubFolders) {
            $subFolderPath = if ($FolderPath -eq "" -or $FolderPath -eq "/") {
                "/$($subFolder.Name)"
            } else {
                "$FolderPath/$($subFolder.Name)"
            }

            # 再帰的にサブフォルダを処理
            try {
                $recursiveFolders = Get-FoldersRecursively -LibraryName $LibraryName -FolderPath $subFolderPath -MaxDepth $MaxDepth -CurrentDepth ($CurrentDepth + 1)
                $folders += $recursiveFolders
            }
            catch {
                Write-Log "Failed to process subfolder recursively: $subFolderPath - $($_.Exception.Message)" "WARNING"
            }
        }

        return $folders
    }
    catch {
        Write-Log "Failed to get folders recursively from $FolderPath: $($_.Exception.Message)" "WARNING"
        return $folders
    }
}

# ファイルメタデータ取得関数（改善版）
function Get-SharePointFileMetadata {
    param(
        [string]$LibraryName,
        [string]$TargetPath,
        [string]$SiteUrl
    )

    try {
        Write-Log "Getting file metadata from library: $LibraryName, path: $TargetPath"

        $files = @()
        $processedFolders = 0

        # フォルダ一覧を再帰的に取得
        Write-Log "Discovering folder structure..."
        $folders = Get-FoldersRecursively -LibraryName $LibraryName -FolderPath $TargetPath -MaxDepth 20

        Write-Log "Found $($folders.Count) folders to process"

        foreach ($folderPath in $folders) {
            try {
                $processedFolders++

                # 進捗表示
                if ($folders.Count -gt 1) {
                    $percent = [math]::Round(($processedFolders / $folders.Count) * 100, 1)
                    Write-Progress -Activity "Scanning folders" -Status "Processing folder $processedFolders of $($folders.Count) ($percent%)" -PercentComplete $percent
                }

                # フォルダ内のファイルを取得（改善されたページング使用）
                $folderFiles = @()

                # リトライ機能付きでファイルを取得
                $folderFiles = Invoke-WithRetry -OperationName "Get files from folder $folderPath" -ScriptBlock {
                    # フォルダパスの構築
                    $fullFolderPath = if ($folderPath -eq "" -or $folderPath -eq "/") {
                        $LibraryName
                    } else {
                        "$LibraryName$folderPath"
                    }

                    # 単一のAPI呼び出しでファイルを取得（ページサイズを使用）
                    $pageItems = Get-PnPFolderItem -FolderSiteRelativeUrl $fullFolderPath -ItemType File -ErrorAction Stop

                    Write-Log "Folder: $folderPath - Found $($pageItems.Count) files" "INFO"
                    return $pageItems
                }

                Write-Log "Folder: $folderPath - Total files found: $($folderFiles.Count)"

                # ファイル情報を処理
                foreach ($file in $folderFiles) {
                    try {
                        # 相対パスの計算
                        $relativePath = if ($folderPath -eq "" -or $folderPath -eq "/") {
                            $file.Name
                        } else {
                            "$($folderPath.TrimStart('/'))/$($file.Name)"
                        }

                        $fileInfo = [PSCustomObject]@{
                            FilePath      = $relativePath
                            FileName      = $file.Name
                            FileSize      = $file.Length
                            LastModified  = $file.TimeLastModified
                            SharePointURL = $SiteUrl
                            FolderPath    = $folderPath
                        }

                        $files += $fileInfo

                    }
                    catch {
                        Write-Log "Failed to process file: $($file.Name) in folder $folderPath - $($_.Exception.Message)" "WARNING"
                    }
                }

            }
            catch {
                Write-Log "Failed to process folder: $folderPath - $($_.Exception.Message)" "WARNING"
                continue
            }
        }

        # 進捗バーをクリア
        Write-Progress -Activity "Scanning folders" -Completed

        Write-Log "Total files found: $($files.Count)"
        return $files

    }
    catch {
        Write-Log "Failed to get file metadata: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# SharePoint処理関数
function Process-SharePointLibrary {
    param(
        [PSCustomObject]$Config,
        [hashtable]$AppCredentials,
        [string]$OutputDir
    )

    $script:CurrentLibrary = "$($Config.SiteUrl) - $($Config.LibraryName)"

    try {
        # 既存出力ファイルのチェック（レジューム機能）
        if ($ResumeMode -and (Test-ExistingOutput -SiteUrl $Config.SiteUrl -LibraryName $Config.LibraryName -TargetPath $Config.TargetPath)) {
            Write-Log "Skipping already processed library: $($script:CurrentLibrary)" "WARNING"
            return
        }

        # SharePoint 証明書認証接続
        if (-not (Connect-SharePointSite -SiteUrl $Config.SiteUrl -CertCredentials $AppCredentials)) {
            throw "Failed to connect to SharePoint site using certificate authentication"
        }

        # ファイルメタデータ取得
        $files = Get-SharePointFileMetadata -LibraryName $Config.LibraryName -TargetPath $Config.TargetPath -SiteUrl $Config.SiteUrl

        if ($files.Count -eq 0) {
            Write-Log "No files found in the specified location" "WARNING"
            return
        }

        # バッチ処理でCSV出力
        $batchData = @()
        $isFirstBatch = $true
        $processedCount = 0
        $batchNumber = 1
        $outputFiles = @()

        # ファイル数が BatchSize より多い場合は複数バッチに分割
        $willUseBatches = $files.Count -gt $BatchSize

        foreach ($file in $files) {
            $batchData += $file
            $processedCount++
            $script:ProcessedFiles++

            # 進捗表示
            Show-Progress -Current $processedCount -Total $files.Count -Activity "Processing $($script:CurrentLibrary)"

            # バッチサイズに達したら書き込み
            if ($batchData.Count -ge $BatchSize) {
                if ($willUseBatches) {
                    # 複数バッチの場合はバッチ番号付きファイル名を使用
                    $batchOutputPath = Write-CsvBatch -FileData $batchData -SiteUrl $Config.SiteUrl -LibraryName $Config.LibraryName -TargetPath $Config.TargetPath -OutputDir $OutputDir -IsFirstBatch $isFirstBatch -BatchNumber $batchNumber
                    $outputFiles += $batchOutputPath
                    $batchNumber++
                }
                else {
                    # 単一バッチの場合は通常のファイル名を使用（BatchNumber = 0）
                    $batchOutputPath = Write-CsvBatch -FileData $batchData -SiteUrl $Config.SiteUrl -LibraryName $Config.LibraryName -TargetPath $Config.TargetPath -OutputDir $OutputDir -IsFirstBatch $isFirstBatch -BatchNumber 0
                    $outputFiles += $batchOutputPath
                }
                $batchData = @()
                $isFirstBatch = $false
            }
        }

        # 残りのデータを書き込み
        if ($batchData.Count -gt 0) {
            if ($willUseBatches -and $batchNumber -gt 1) {
                # 複数バッチの場合はバッチ番号付きファイル名を使用
                $batchOutputPath = Write-CsvBatch -FileData $batchData -SiteUrl $Config.SiteUrl -LibraryName $Config.LibraryName -TargetPath $Config.TargetPath -OutputDir $OutputDir -IsFirstBatch $isFirstBatch -BatchNumber $batchNumber
                $outputFiles += $batchOutputPath
            }
            else {
                # 単一バッチまたは最初のバッチの場合は通常のファイル名を使用（BatchNumber = 0）
                $batchOutputPath = Write-CsvBatch -FileData $batchData -SiteUrl $Config.SiteUrl -LibraryName $Config.LibraryName -TargetPath $Config.TargetPath -OutputDir $OutputDir -IsFirstBatch $isFirstBatch -BatchNumber 0
                $outputFiles += $batchOutputPath
            }
        }

        # 成功メッセージの表示
        if ($outputFiles.Count -eq 1) {
            $outputFileName = Split-Path $outputFiles[0] -Leaf
            Write-Log "Successfully processed library: $($script:CurrentLibrary) - $($files.Count) files exported to $outputFileName" "SUCCESS"
        }
        else {
            $outputFileNames = $outputFiles | ForEach-Object { Split-Path $_ -Leaf }
            Write-Log "Successfully processed library: $($script:CurrentLibrary) - $($files.Count) files exported to $($outputFiles.Count) batch files:" "SUCCESS"
            foreach ($fileName in $outputFileNames) {
                Write-Log "  - $fileName" "SUCCESS"
            }
        }

    }
    catch {
        Write-Log "Failed to process library $($script:CurrentLibrary): $($_.Exception.Message)" "ERROR"
    }
    finally {
        # SharePoint接続を切断
        try {
            Disconnect-PnPOnline -ErrorAction SilentlyContinue
        }
        catch {
            # 切断エラーは無視
        }
    }
}

# メイン実行部
function Main {
    try {
        # スクリプトレベル変数の初期化
        $script:MaxRetries = $MaxRetries
        $script:RetryDelaySeconds = $RetryDelaySeconds
        $script:MaxFolderDepth = $MaxFolderDepth

        Write-Log "SharePoint File List Script Started (Enhanced Version)" "SUCCESS"
        Write-Log "Parameters: ConfigFile=$ConfigFile, CredentialsFile=$CredentialsFile, OutputDirectory=$OutputDirectory" "INFO"
        Write-Log "BatchSize=$BatchSize, PageSize=$PageSize, MaxRetries=$MaxRetries, RetryDelaySeconds=$RetryDelaySeconds" "INFO"
        Write-Log "MaxFolderDepth=$MaxFolderDepth, VerboseLogging=$VerboseLogging, ResumeMode=$ResumeMode" "INFO"

        # 初期メモリ使用量を記録
        $initialMemory = Get-MemoryUsage
        Write-Log "Initial memory usage: ${initialMemory}MB" "INFO"

        # 出力ディレクトリの作成
        if (-not (Test-Path $OutputDirectory)) {
            New-Item -ItemType Directory -Path $OutputDirectory -Force | Out-Null
            Write-Log "Created output directory: $OutputDirectory" "SUCCESS"
        }

        # 必要なモジュールの確認
        if (-not (Get-Module -ListAvailable -Name PnP.PowerShell)) {
            Write-Log "PnP.PowerShell module not found. Installing..." "WARNING"
            try {
                Install-Module -Name PnP.PowerShell -Force -AllowClobber -Scope CurrentUser
                Write-Log "PnP.PowerShell module installed successfully" "SUCCESS"
            }
            catch {
                Write-Log "Failed to install PnP.PowerShell module: $($_.Exception.Message)" "ERROR"
                Write-Log "Please install manually using: Install-Module -Name PnP.PowerShell" "ERROR"
                exit 1
            }
        }

        # 証明書認証情報の読み込み
        Write-Log "Loading certificate credentials from: $CredentialsFile"
        $appCredentials = Get-CertificateCredentials -CredentialsPath $CredentialsFile

        # 設定ファイルの読み込み
        Write-Log "Loading SharePoint configurations from: $ConfigFile"
        $configs = Get-SharePointConfig -ConfigPath $ConfigFile

        if ($configs.Count -eq 0) {
            Write-Log "No valid configurations found in the config file" "ERROR"
            exit 1
        }

        # 処理開始
        Write-Log "Starting processing of $($configs.Count) SharePoint library configurations"
        $script:TotalEstimatedFiles = $configs.Count * 1000  # 概算値

        $successCount = 0
        $errorCount = 0

        foreach ($config in $configs) {
            try {
                Write-Log "Processing configuration: $($config.SiteUrl) | $($config.LibraryName) | $($config.TargetPath)"

                # メモリ使用量をチェック
                $currentMemory = Get-MemoryUsage
                if ($currentMemory -gt 2048) { # 2GB以上の場合
                    Write-Log "High memory usage detected (${currentMemory}MB), performing garbage collection" "WARNING"
                    Invoke-GarbageCollection -Context "before processing $($config.LibraryName)"
                }

                Process-SharePointLibrary -Config $config -AppCredentials $appCredentials -OutputDir $OutputDirectory
                $successCount++

                # 各ライブラリ処理後にガベージコレクション
                if ($successCount % 3 -eq 0) {
                    Invoke-GarbageCollection -Context "after processing $successCount libraries"
                }

            }
            catch {
                Write-Log "Failed to process configuration: $($_.Exception.Message)" "ERROR"
                $errorCount++
            }
        }

        # 最終的なメモリクリーンアップ
        Invoke-GarbageCollection -Context "final cleanup"

        # 処理完了
        $totalTime = (Get-Date) - $script:StartTime
        $finalMemory = Get-MemoryUsage
        $memoryDiff = $finalMemory - $initialMemory

        Write-Log "Processing completed!" "SUCCESS"
        Write-Log "=== PROCESSING SUMMARY ===" "SUCCESS"
        Write-Log "Total configurations processed: $($configs.Count)" "SUCCESS"
        Write-Log "Successful: $successCount, Errors: $errorCount" "SUCCESS"
        Write-Log "Total files processed: $script:ProcessedFiles" "SUCCESS"
        Write-Log "Total execution time: $($totalTime.ToString('hh\:mm\:ss'))" "SUCCESS"
        Write-Log "Memory usage - Initial: ${initialMemory}MB, Final: ${finalMemory}MB, Difference: ${memoryDiff}MB" "SUCCESS"
        Write-Log "Output directory: $OutputDirectory" "SUCCESS"
        Write-Log "=========================" "SUCCESS"

        # 進捗バーをクリア
        Write-Progress -Activity "Processing SharePoint Libraries" -Completed

    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" "ERROR"
        Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
        exit 1
    }

    # スクリプト終了
    Write-Log "SharePoint File List Script Completed" "SUCCESS"
}

# スクリプト実行
Main