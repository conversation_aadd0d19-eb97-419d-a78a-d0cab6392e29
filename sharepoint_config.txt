# SharePoint ライブラリ設定ファイル
# - 各行は SharePointSiteURL|DocumentLibraryName|TargetDirectoryPath の形式で記述
# - TargetDirectoryPath は "/" でルートディレクトリを指定、空文字でも可
# - # で始まる行はコメントとして無視されます

# 例1: プロジェクトサイトのドキュメントライブラリ全体
# https://company.sharepoint.com/sites/project1|Documents|/

# 例2: 特定のフォルダのみ
# https://company.sharepoint.com/sites/project1|Documents|/Shared Documents/Reports

# 例3: 別のサイトの共有ドキュメント
# https://company.sharepoint.com/sites/project2|Shared Documents|/

# 例4: カスタムライブラリ
# https://company.sharepoint.com/sites/project3|CustomLibrary|/Archive/2024

# 例5: チームサイト
# https://company.sharepoint.com/teams/development|Documents|/Projects/Current

# https://mitsubishishokuhin365.sharepoint.com/teams/SVN|Shared Documents|/ハイロンテスト用
# https://mitsubishishokuhin365.sharepoint.com/teams/SVN|Shared Documents|/
# https://hyronjp.sharepoint.com/sites/eneos-_SVN|Shared Documents/三菱食品_SVN移行/99.test/ProjectWEB|/trunk/共通
https://hyronjp.sharepoint.com/sites/eneos-_SVN|Shared Documents|/三菱食品_SVN移行/99.test/三菱食品ProjectWEB
# https://hyronjp.sharepoint.com/sites/eneos-_SVN|Shared Documents/三菱食品_SVN移行|/99.test/ProjectWEB




