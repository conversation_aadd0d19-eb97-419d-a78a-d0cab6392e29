# SharePoint ファイルインベントリスクリプト（最適化版）
# 並行処理、バッチAPI、ストリーミング処理による高速化対応

param(
    [Parameter(Mandatory = $false, HelpMessage = "SharePoint ライブラリ設定ファイルのパス")]
    [string]$ConfigFile = "./sharepoint_config.txt",

    [Parameter(Mandatory = $false, HelpMessage = "認証情報JSONファイルのパス")]
    [string]$CredentialsFile = "./credentials.json",

    [Parameter(Mandatory = $false, HelpMessage = "CSV出力ディレクトリ")]
    [string]$OutputDirectory = "./output",

    [Parameter(Mandatory = $false, HelpMessage = "CSVバッチサイズ(デフォルト: 10000)")]
    [int]$BatchSize = 10000,

    [Parameter(Mandatory = $false, HelpMessage = "詳細ログを有効にする")]
    [switch]$VerboseLogging = $false,

    [Parameter(Mandatory = $false, HelpMessage = "処理済みリポジトリを自動スキップ")]
    [switch]$ResumeMode = $false,

    [Parameter(Mandatory = $false, HelpMessage = "並行処理スレッド数(デフォルト: 4)")]
    [int]$MaxParallelJobs = 4,

    [Parameter(Mandatory = $false, HelpMessage = "SharePoint APIページサイズ(デフォルト: 5000)")]
    [int]$ApiPageSize = 5000,

    [Parameter(Mandatory = $false, HelpMessage = "エラー時の最大リトライ回数(デフォルト: 3)")]
    [int]$MaxRetries = 3,

    [Parameter(Mandatory = $false, HelpMessage = "リトライ間隔（秒）(デフォルト: 2)")]
    [int]$RetryDelaySeconds = 2,

    [Parameter(Mandatory = $false, HelpMessage = "ストリーミング書き込みを有効にする（メモリ使用量削減）")]
    [switch]$StreamingMode = $true
)

# グローバル変数の初期化
$script:ProcessedFiles = 0
$script:TotalEstimatedFiles = 0
$script:StartTime = Get-Date
$script:GlobalLogFile = ""

# ログ関数（並行処理対応）
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$JobId = ""
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $jobPrefix = if ($JobId) { "[$JobId] " } else { "" }
    $logMessage = "[$timestamp] [$Level] $jobPrefix$Message"

    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARNING" { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage }
    }

    if ($VerboseLogging -and $script:GlobalLogFile) {
        $mutex = New-Object System.Threading.Mutex($false, "SharePointLogMutex")
        try {
            $mutex.WaitOne() | Out-Null
            Add-Content -Path $script:GlobalLogFile -Value $logMessage -Encoding UTF8
        }
        finally {
            $mutex.ReleaseMutex()
        }
    }
}

# 設定ファイル読み込み関数
function Get-SharePointConfig {
    param([string]$ConfigPath)
    
    try {
        if (-not (Test-Path $ConfigPath)) {
            throw "Configuration file not found: $ConfigPath"
        }
        
        $configs = @()
        $lines = Get-Content $ConfigPath -Encoding UTF8
        
        foreach ($line in $lines) {
            $line = $line.Trim()
            if ($line -and -not $line.StartsWith("#")) {
                $parts = $line -split '\|'
                if ($parts.Count -eq 3) {
                    $configs += [PSCustomObject]@{
                        SiteUrl     = $parts[0].Trim()
                        LibraryName = $parts[1].Trim()
                        TargetPath  = $parts[2].Trim()
                        ConfigIndex = $configs.Count
                    }
                }
            }
        }
        
        Write-Log "Loaded $($configs.Count) SharePoint library configurations" "SUCCESS"
        return $configs
        
    }
    catch {
        Write-Log "Failed to load configuration: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 証明書認証情報読み込み関数
function Get-CertificateCredentials {
    param([string]$CredentialsPath)
    
    try {
        if (-not (Test-Path $CredentialsPath)) {
            throw "Credentials file not found: $CredentialsPath"
        }
        
        $credJson = Get-Content $CredentialsPath -Raw -Encoding UTF8 | ConvertFrom-Json
        
        if (-not $credJson.clientId -or -not $credJson.certificatePath -or -not $credJson.tenantId) {
            throw "Invalid certificate credentials format"
        }
        
        if (-not (Test-Path $credJson.certificatePath)) {
            throw "Certificate file not found: $($credJson.certificatePath)"
        }
        
        return @{
            ClientId            = $credJson.clientId
            CertificatePath     = $credJson.certificatePath
            CertificatePassword = $credJson.certificatePassword
            TenantId            = $credJson.tenantId
        }
        
    }
    catch {
        Write-Log "Failed to load certificate credentials: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 出力ファイル名生成関数
function Get-OutputFileName {
    param(
        [string]$SiteUrl,
        [string]$LibraryName,
        [string]$TargetPath,
        [int]$ConfigIndex = 0
    )

    $siteName = ($SiteUrl -split '/')[-1] -replace '[^\w\-_]', '_'
    $libraryName = $LibraryName -replace '[^\w\-_]', '_'
    
    $targetDir = if ($TargetPath -eq "/" -or $TargetPath -eq "" -or $TargetPath -eq $null) {
        ""
    }
    else {
        ($TargetPath.TrimStart('/').TrimEnd('/') -replace '[^\w\-_/]', '_') -replace '/', '_'
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    return "${siteName}_${libraryName}_${targetDir}_${ConfigIndex}_${timestamp}.csv"
}

# SharePoint 接続関数（最適化版）
function Connect-SharePointSiteOptimized {
    param(
        [string]$SiteUrl,
        [hashtable]$CertCredentials,
        [string]$JobId = ""
    )

    try {
        Write-Log "Connecting to SharePoint site: $SiteUrl" "INFO" $JobId
        
        $securePassword = $null
        if ($CertCredentials.CertificatePassword) {
            if ($CertCredentials.CertificatePassword -is [SecureString]) {
                $securePassword = $CertCredentials.CertificatePassword
            }
            else {
                $securePassword = ConvertTo-SecureString -String $CertCredentials.CertificatePassword -AsPlainText -Force
            }
        }

        # 証明書認証でSharePoint接続（タイムアウト設定を追加）
        $connectParams = @{
            Url = $SiteUrl
            ClientId = $CertCredentials.ClientId
            CertificatePath = $CertCredentials.CertificatePath
            Tenant = $CertCredentials.TenantId
            WarningAction = 'SilentlyContinue'
            ErrorAction = 'Stop'
        }
        
        if ($securePassword) {
            $connectParams.CertificatePassword = $securePassword
        }

        Connect-PnPOnline @connectParams
        
        Write-Log "Successfully connected to SharePoint site" "SUCCESS" $JobId
        return $true

    }
    catch {
        Write-Log "Failed to connect to SharePoint: $($_.Exception.Message)" "ERROR" $JobId
        return $false
    }
}

# 最適化されたファイルメタデータ取得関数（CAML/REST API使用）
function Get-SharePointFileMetadataOptimized {
    param(
        [string]$LibraryName,
        [string]$TargetPath,
        [string]$SiteUrl,
        [string]$JobId = ""
    )

    try {
        Write-Log "Getting file metadata from library: $LibraryName" "INFO" $JobId
        
        # CAMLクエリを使用して効率的にファイル情報を取得
        $camlQuery = @"
<View Scope='RecursiveAll'>
    <Query>
        <Where>
            <Eq>
                <FieldRef Name='FSObjType'/>
                <Value Type='Integer'>0</Value>
            </Eq>
        </Where>
        <OrderBy>
            <FieldRef Name='FileLeafRef' Ascending='TRUE'/>
        </OrderBy>
    </Query>
    <ViewFields>
        <FieldRef Name='FileLeafRef'/>
        <FieldRef Name='FileDirRef'/>
        <FieldRef Name='File_x0020_Size'/>
        <FieldRef Name='Modified'/>
        <FieldRef Name='FileRef'/>
    </ViewFields>
    <RowLimit Paged='TRUE'>$ApiPageSize</RowLimit>
</View>
"@

        $files = @()
        $listItems = @()
        
        try {
            # ライブラリを取得
            $list = Get-PnPList -Identity $LibraryName -ErrorAction Stop
            
            # ページング処理でアイテムを取得
            $page = 0
            do {
                $page++
                Write-Log "Fetching page $page from library $LibraryName" "INFO" $JobId
                
                if ($page -eq 1) {
                    $items = Get-PnPListItem -List $list -Query $camlQuery -ErrorAction Stop
                }
                else {
                    $items = Get-PnPListItem -List $list -Query $camlQuery -PageSize $ApiPageSize -ErrorAction Stop
                }
                
                $listItems += $items
                Write-Log "Retrieved $($items.Count) items from page $page" "INFO" $JobId
                
            } while ($items.Count -eq $ApiPageSize)
            
        }
        catch {
            Write-Log "CAML query failed, falling back to folder iteration: $($_.Exception.Message)" "WARNING" $JobId
            # フォールバック：従来の方法
            return Get-SharePointFileMetadataFallback -LibraryName $LibraryName -TargetPath $TargetPath -SiteUrl $SiteUrl -JobId $JobId
        }

        Write-Log "Processing $($listItems.Count) list items" "INFO" $JobId

        # 並行処理前に変数を準備
        $targetPathNormalized = if ($TargetPath -and $TargetPath -ne "/" -and $TargetPath -ne "") {
            $TargetPath.TrimStart('/').TrimEnd('/')
        } else { "" }
        
        $libraryBasePath = "/$LibraryName"
        $expectedPath = if ($targetPathNormalized) { "$LibraryName/$targetPathNormalized" } else { "" }
        $libraryBasePathLength = $libraryBasePath.Length

        # リストアイテムを並行処理で変換
        $files = $listItems | ForEach-Object -Parallel {
            try {
                $item = $_
                $fileRef = $item.FieldValues.FileRef
                $fileDirRef = $item.FieldValues.FileDirRef
                $fileName = $item.FieldValues.FileLeafRef
                
                # ターゲットパスフィルタリング
                if ($using:expectedPath -and -not $fileDirRef.StartsWith($using:expectedPath)) {
                    return $null
                }
                
                # フォルダパスを取得
                $folderPath = if ($fileDirRef.StartsWith($using:libraryBasePath)) {
                    $tempPath = $fileDirRef.Substring($using:libraryBasePathLength)
                    if ($tempPath.StartsWith('/')) { $tempPath.Substring(1) } else { $tempPath }
                } else { "" }

                # 相対パスを計算
                $relativePath = if ($folderPath) { "$folderPath/$fileName" } else { $fileName }

                return [PSCustomObject]@{
                    FilePath      = $relativePath
                    FileName      = $fileName
                    FileSize      = $item.FieldValues.File_x0020_Size
                    LastModified  = $item.FieldValues.Modified
                    SharePointURL = $using:SiteUrl
                    FolderPath    = $folderPath
                }
            }
            catch {
                Write-Warning "Failed to process item: $($_.Exception.Message)"
                return $null
            }
        } -ThrottleLimit 10

        # null値を除外
        $files = $files | Where-Object { $_ -ne $null }
        
        Write-Log "Processed $($files.Count) files from library $LibraryName" "SUCCESS" $JobId
        return $files

    }
    catch {
        Write-Log "Failed to get file metadata: $($_.Exception.Message)" "ERROR" $JobId
        throw
    }
}

# フォールバック用のファイルメタデータ取得関数
function Get-SharePointFileMetadataFallback {
    param(
        [string]$LibraryName,
        [string]$TargetPath,
        [string]$SiteUrl,
        [string]$JobId = ""
    )

    try {
        Write-Log "Using fallback method for file metadata retrieval" "WARNING" $JobId
        
        $allFiles = Get-PnPFolderItem -FolderSiteRelativeUrl $LibraryName -ItemType File -Recursive -ErrorAction Stop
        
        $files = @()
        foreach ($file in $allFiles) {
            try {
                $fileInfo = [PSCustomObject]@{
                    FilePath      = $file.ServerRelativeUrl.Replace("/$LibraryName/", "")
                    FileName      = $file.Name
                    FileSize      = $file.Length
                    LastModified  = $file.TimeLastModified
                    SharePointURL = $SiteUrl
                    FolderPath    = ""
                }
                $files += $fileInfo
            }
            catch {
                Write-Log "Failed to process file: $($file.Name)" "WARNING" $JobId
            }
        }
        
        return $files
    }
    catch {
        Write-Log "Fallback method also failed: $($_.Exception.Message)" "ERROR" $JobId
        throw
    }
}

# ストリーミングCSV書き込み関数
function Write-CsvStreaming {
    param(
        [array]$FileData,
        [string]$FilePath,
        [bool]$WriteHeader = $true
    )

    try {
        $streamWriter = $null
        
        try {
            $streamWriter = [System.IO.StreamWriter]::new($FilePath, $false, [System.Text.Encoding]::UTF8)
            
            if ($WriteHeader) {
                $header = '"FilePath","FileName","FileSize","LastModified","SharePointURL","FolderPath"'
                $streamWriter.WriteLine($header)
            }
            
            foreach ($file in $FileData) {
                $line = '"{0}","{1}","{2}","{3}","{4}","{5}"' -f 
                    $file.FilePath, $file.FileName, $file.FileSize, 
                    $file.LastModified, $file.SharePointURL, $file.FolderPath
                $streamWriter.WriteLine($line)
            }
            
            $streamWriter.Flush()
        }
        finally {
            if ($streamWriter) {
                $streamWriter.Dispose()
            }
        }
        
        return $true
    }
    catch {
        Write-Log "Failed to write CSV streaming: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 並行処理用のSharePointライブラリ処理関数
function Process-SharePointLibraryJob {
    param(
        [PSCustomObject]$Config,
        [hashtable]$AppCredentials,
        [string]$OutputDir,
        [string]$JobId
    )

    try {
        Write-Log "Starting processing of library: $($Config.LibraryName)" "INFO" $JobId
        
        # SharePoint接続
        if (-not (Connect-SharePointSiteOptimized -SiteUrl $Config.SiteUrl -CertCredentials $AppCredentials -JobId $JobId)) {
            throw "Failed to connect to SharePoint site"
        }

        # ファイルメタデータ取得（最適化版）
        $files = Get-SharePointFileMetadataOptimized -LibraryName $Config.LibraryName -TargetPath $Config.TargetPath -SiteUrl $Config.SiteUrl -JobId $JobId

        if ($files.Count -eq 0) {
            Write-Log "No files found in the specified location" "WARNING" $JobId
            return @{
                Success = $true
                FileCount = 0
                OutputPath = ""
            }
        }

        # CSV出力
        $fileName = Get-OutputFileName -SiteUrl $Config.SiteUrl -LibraryName $Config.LibraryName -TargetPath $Config.TargetPath -ConfigIndex $Config.ConfigIndex
        $outputPath = Join-Path $OutputDir $fileName

        if ($StreamingMode) {
            $success = Write-CsvStreaming -FileData $files -FilePath $outputPath -WriteHeader $true
        }
        else {
            $files | Export-Csv -Path $outputPath -NoTypeInformation -Encoding UTF8
            $success = $true
        }

        if ($success) {
            Write-Log "Successfully processed library: $($Config.LibraryName) - $($files.Count) files exported to $fileName" "SUCCESS" $JobId
        }

        return @{
            Success = $success
            FileCount = $files.Count
            OutputPath = $outputPath
        }

    }
    catch {
        Write-Log "Failed to process library $($Config.LibraryName): $($_.Exception.Message)" "ERROR" $JobId
        return @{
            Success = $false
            FileCount = 0
            OutputPath = ""
            Error = $_.Exception.Message
        }
    }
    finally {
        try {
            Disconnect-PnPOnline -ErrorAction SilentlyContinue
        }
        catch {
            # 切断エラーは無視
        }
    }
}

# メイン実行部（最適化版）
function Main {
    try {
        Write-Log "SharePoint File List Script Started (Optimized Version)" "SUCCESS"
        Write-Log "Parameters: MaxParallelJobs=$MaxParallelJobs, ApiPageSize=$ApiPageSize, StreamingMode=$StreamingMode" "INFO"

        # グローバルログファイルの設定
        if ($VerboseLogging) {
            $script:GlobalLogFile = Join-Path $OutputDirectory "SharePoint-FileList-Optimized.log"
        }

        # 出力ディレクトリの作成
        if (-not (Test-Path $OutputDirectory)) {
            New-Item -ItemType Directory -Path $OutputDirectory -Force | Out-Null
            Write-Log "Created output directory: $OutputDirectory" "SUCCESS"
        }

        # PnP.PowerShell モジュールの確認
        if (-not (Get-Module -ListAvailable -Name PnP.PowerShell)) {
            Write-Log "Installing PnP.PowerShell module..." "WARNING"
            Install-Module -Name PnP.PowerShell -Force -AllowClobber -Scope CurrentUser
        }

        # 設定とクレデンシャルの読み込み
        $appCredentials = Get-CertificateCredentials -CredentialsPath $CredentialsFile
        $configs = Get-SharePointConfig -ConfigPath $ConfigFile

        if ($configs.Count -eq 0) {
            Write-Log "No valid configurations found" "ERROR"
            exit 1
        }

        Write-Log "Starting parallel processing of $($configs.Count) SharePoint libraries with $MaxParallelJobs threads"

        # 並行処理でSharePointライブラリを処理
        $jobs = @()
        $jobResults = @()
        
        # バッチ処理で並行ジョブを管理
        for ($i = 0; $i -lt $configs.Count; $i += $MaxParallelJobs) {
            $batchConfigs = $configs[$i..([Math]::Min($i + $MaxParallelJobs - 1, $configs.Count - 1))]
            
            Write-Log "Processing batch $([Math]::Floor($i / $MaxParallelJobs) + 1) with $($batchConfigs.Count) libraries"
            
            # 現在のバッチを並行処理
            $batchResults = $batchConfigs | ForEach-Object -Parallel {
                $config = $_
                $jobId = "Job-$($config.ConfigIndex)"
                
                # 並行処理用のモジュールインポート
                Import-Module PnP.PowerShell -Force -ErrorAction SilentlyContinue
                
                # 外部関数を並行処理スコープ内で再定義
                $result = & {
                    param($Config, $AppCredentials, $OutputDir, $JobId)
                    
                    # ここに Process-SharePointLibraryJob の機能を直接実装
                    try {
                        # PnP 接続
                        $connectParams = @{
                            Url = $Config.SiteUrl
                            ClientId = $AppCredentials.ClientId
                            CertificatePath = $AppCredentials.CertificatePath
                            Tenant = $AppCredentials.TenantId
                            WarningAction = 'SilentlyContinue'
                            ErrorAction = 'Stop'
                        }
                        
                        if ($AppCredentials.CertificatePassword) {
                            $securePassword = ConvertTo-SecureString -String $AppCredentials.CertificatePassword -AsPlainText -Force
                            $connectParams.CertificatePassword = $securePassword
                        }

                        Connect-PnPOnline @connectParams
                        
                        # ファイル取得
                        $list = Get-PnPList -Identity $Config.LibraryName
                        $allFiles = Get-PnPFolderItem -FolderSiteRelativeUrl $Config.LibraryName -ItemType File -Recursive
                        
                        $files = $allFiles | ForEach-Object {
                            [PSCustomObject]@{
                                FilePath      = $_.ServerRelativeUrl.Replace("/$($Config.LibraryName)/", "")
                                FileName      = $_.Name
                                FileSize      = $_.Length
                                LastModified  = $_.TimeLastModified
                                SharePointURL = $Config.SiteUrl
                                FolderPath    = ""
                            }
                        }
                        
                        # CSV出力
                        $siteName = ($Config.SiteUrl -split '/')[-1] -replace '[^\w\-_]', '_'
                        $libraryName = $Config.LibraryName -replace '[^\w\-_]', '_'
                        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
                        $fileName = "${siteName}_${libraryName}_$($Config.ConfigIndex)_${timestamp}.csv"
                        $outputPath = Join-Path $OutputDir $fileName
                        
                        $files | Export-Csv -Path $outputPath -NoTypeInformation -Encoding UTF8
                        
                        Disconnect-PnPOnline -ErrorAction SilentlyContinue
                        
                        return @{
                            Success = $true
                            FileCount = $files.Count
                            OutputPath = $outputPath
                            ConfigIndex = $Config.ConfigIndex
                        }
                    }
                    catch {
                        Disconnect-PnPOnline -ErrorAction SilentlyContinue
                        return @{
                            Success = $false
                            FileCount = 0
                            OutputPath = ""
                            Error = $_.Exception.Message
                            ConfigIndex = $Config.ConfigIndex
                        }
                    }
                } -ArgumentList $config, $using:appCredentials, $using:OutputDirectory, $jobId
                
                return $result
                
            } -ThrottleLimit $MaxParallelJobs
            
            $jobResults += $batchResults
            
            # バッチ完了後の進捗表示
            Write-Log "Completed batch $([Math]::Floor($i / $MaxParallelJobs) + 1)"
        }

        # 結果の集計
        $successCount = ($jobResults | Where-Object { $_.Success }).Count
        $totalFiles = ($jobResults | Measure-Object -Property FileCount -Sum).Sum
        $totalTime = (Get-Date) - $script:StartTime

        Write-Log "Processing completed!" "SUCCESS"
        Write-Log "=== PROCESSING SUMMARY ===" "SUCCESS"
        Write-Log "Total configurations: $($configs.Count)" "SUCCESS"
        Write-Log "Successful: $successCount, Failed: $($configs.Count - $successCount)" "SUCCESS"
        Write-Log "Total files processed: $totalFiles" "SUCCESS"
        Write-Log "Total execution time: $($totalTime.ToString('hh\:mm\:ss'))" "SUCCESS"
        Write-Log "Average processing speed: $([Math]::Round($totalFiles / $totalTime.TotalSeconds, 2)) files/second" "SUCCESS"
        Write-Log "Output directory: $OutputDirectory" "SUCCESS"
        Write-Log "=========================" "SUCCESS"

    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" "ERROR"
        Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
        exit 1
    }
}

# スクリプト実行
Main
