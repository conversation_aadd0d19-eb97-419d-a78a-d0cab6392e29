# SharePoint ファイルリストスクリプト テスト用
# 改善版スクリプトの動作確認用

param(
    [Parameter(Mandatory = $false)]
    [string]$TestMode = "DryRun"  # DryRun, Small, Medium, Large
)

function Test-ScriptParameters {
    Write-Host "=== SharePoint スクリプト パラメータテスト ===" -ForegroundColor Green
    
    # 小規模テスト（推奨設定）
    if ($TestMode -eq "Small" -or $TestMode -eq "DryRun") {
        Write-Host "小規模ライブラリ向け設定:" -ForegroundColor Yellow
        Write-Host "  BatchSize: 5000" -ForegroundColor White
        Write-Host "  PageSize: 200" -ForegroundColor White
        Write-Host "  MaxRetries: 3" -ForegroundColor White
        Write-Host "  RetryDelaySeconds: 5" -ForegroundColor White
        Write-Host "  MaxFolderDepth: 20" -ForegroundColor White
        Write-Host ""
        
        if ($TestMode -ne "DryRun") {
            $params = @{
                ConfigFile = "./sharepoint_config_optimized.txt"
                CredentialsFile = "./credentials.json"
                OutputDirectory = "./output/small_test"
                BatchSize = 5000
                PageSize = 200
                MaxRetries = 3
                RetryDelaySeconds = 5
                MaxFolderDepth = 20
                VerboseLogging = $true
            }
        }
    }
    
    # 中規模テスト
    if ($TestMode -eq "Medium") {
        Write-Host "中規模ライブラリ向け設定:" -ForegroundColor Yellow
        Write-Host "  BatchSize: 3000" -ForegroundColor White
        Write-Host "  PageSize: 150" -ForegroundColor White
        Write-Host "  MaxRetries: 5" -ForegroundColor White
        Write-Host "  RetryDelaySeconds: 8" -ForegroundColor White
        Write-Host "  MaxFolderDepth: 15" -ForegroundColor White
        Write-Host ""
        
        $params = @{
            ConfigFile = "./sharepoint_config_optimized.txt"
            CredentialsFile = "./credentials.json"
            OutputDirectory = "./output/medium_test"
            BatchSize = 3000
            PageSize = 150
            MaxRetries = 5
            RetryDelaySeconds = 8
            MaxFolderDepth = 15
            VerboseLogging = $true
        }
    }
    
    # 大規模テスト
    if ($TestMode -eq "Large") {
        Write-Host "大規模ライブラリ向け設定:" -ForegroundColor Yellow
        Write-Host "  BatchSize: 1000" -ForegroundColor White
        Write-Host "  PageSize: 50" -ForegroundColor White
        Write-Host "  MaxRetries: 10" -ForegroundColor White
        Write-Host "  RetryDelaySeconds: 15" -ForegroundColor White
        Write-Host "  MaxFolderDepth: 10" -ForegroundColor White
        Write-Host ""
        
        $params = @{
            ConfigFile = "./sharepoint_config_optimized.txt"
            CredentialsFile = "./credentials.json"
            OutputDirectory = "./output/large_test"
            BatchSize = 1000
            PageSize = 50
            MaxRetries = 10
            RetryDelaySeconds = 15
            MaxFolderDepth = 10
            VerboseLogging = $true
        }
    }
    
    if ($TestMode -ne "DryRun") {
        Write-Host "実行コマンド:" -ForegroundColor Green
        $command = ".\SharePoint-FileList.ps1"
        foreach ($key in $params.Keys) {
            if ($params[$key] -is [bool] -and $params[$key]) {
                $command += " -$key"
            } elseif ($params[$key] -isnot [bool]) {
                $command += " -$key `"$($params[$key])`""
            }
        }
        Write-Host $command -ForegroundColor Cyan
        Write-Host ""
        
        # 実際に実行するかユーザーに確認
        $response = Read-Host "このコマンドを実行しますか？ (y/N)"
        if ($response -eq "y" -or $response -eq "Y") {
            Write-Host "スクリプトを実行中..." -ForegroundColor Green
            & ".\SharePoint-FileList.ps1" @params
        } else {
            Write-Host "実行をキャンセルしました。" -ForegroundColor Yellow
        }
    }
}

function Show-PerformanceComparison {
    Write-Host "=== パフォーマンス比較 ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "改善前の問題:" -ForegroundColor Red
    Write-Host "  ✗ Get-PnPListItem使用によるしきい値エラー" -ForegroundColor Red
    Write-Host "  ✗ 5,000件以上でエラー発生" -ForegroundColor Red
    Write-Host "  ✗ メモリリークによる処理停止" -ForegroundColor Red
    Write-Host "  ✗ エラー時の処理中断" -ForegroundColor Red
    Write-Host ""
    Write-Host "改善後の利点:" -ForegroundColor Green
    Write-Host "  ✓ Get-PnPFolderItem使用でしきい値回避" -ForegroundColor Green
    Write-Host "  ✓ 数万ファイルの大型ライブラリ対応" -ForegroundColor Green
    Write-Host "  ✓ 自動メモリ管理とガベージコレクション" -ForegroundColor Green
    Write-Host "  ✓ 自動リトライ機能" -ForegroundColor Green
    Write-Host "  ✓ フォルダ単位の並列処理" -ForegroundColor Green
    Write-Host "  ✓ 詳細な進捗表示とログ" -ForegroundColor Green
    Write-Host ""
}

function Show-TroubleshootingTips {
    Write-Host "=== トラブルシューティング ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "メモリ不足の場合:" -ForegroundColor Yellow
    Write-Host "  - BatchSizeを1000に削減" -ForegroundColor White
    Write-Host "  - PageSizeを50に削減" -ForegroundColor White
    Write-Host "  - MaxFolderDepthを10に制限" -ForegroundColor White
    Write-Host ""
    Write-Host "ネットワークエラーの場合:" -ForegroundColor Yellow
    Write-Host "  - MaxRetriesを10に増加" -ForegroundColor White
    Write-Host "  - RetryDelaySecondsを30に増加" -ForegroundColor White
    Write-Host ""
    Write-Host "権限エラーの場合:" -ForegroundColor Yellow
    Write-Host "  - Azure ADアプリの権限確認" -ForegroundColor White
    Write-Host "  - 証明書の有効期限確認" -ForegroundColor White
    Write-Host "  - SharePointサイトのアクセス権確認" -ForegroundColor White
    Write-Host ""
}

# メイン実行
Write-Host "SharePoint ファイルリストスクリプト テストツール" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

switch ($TestMode) {
    "DryRun" {
        Write-Host "ドライランモード: 設定のみ表示" -ForegroundColor Blue
        Test-ScriptParameters
        Show-PerformanceComparison
        Show-TroubleshootingTips
    }
    "Small" {
        Write-Host "小規模テストモード" -ForegroundColor Blue
        Test-ScriptParameters
    }
    "Medium" {
        Write-Host "中規模テストモード" -ForegroundColor Blue
        Test-ScriptParameters
    }
    "Large" {
        Write-Host "大規模テストモード" -ForegroundColor Blue
        Test-ScriptParameters
    }
    default {
        Write-Host "使用方法:" -ForegroundColor Yellow
        Write-Host "  .\Test-SharePointScript.ps1 -TestMode DryRun   # 設定確認のみ" -ForegroundColor White
        Write-Host "  .\Test-SharePointScript.ps1 -TestMode Small    # 小規模テスト" -ForegroundColor White
        Write-Host "  .\Test-SharePointScript.ps1 -TestMode Medium   # 中規模テスト" -ForegroundColor White
        Write-Host "  .\Test-SharePointScript.ps1 -TestMode Large    # 大規模テスト" -ForegroundColor White
    }
}
